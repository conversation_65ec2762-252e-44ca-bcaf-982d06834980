<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{ style: { navigationBarTitleText: '规范学习' } }
</route>

<script lang="ts" setup>
import type { ArticleListData, ArticleListRes } from '@/service/tutorialsApi'
import ArticleCard from '@/components/tutorials/articleCard.vue'
import { Color } from '@/enums/colorEnum'
import { articleListApi } from '@/service/tutorialsApi'

const tab = ref<number>(0)
const list = ref<ArticleListData[]>()

const scrollTop = ref<number>(0)
onPageScroll((e) => {
  scrollTop.value = e.scrollTop
})

const {
  loading: tabLoading,
  error: tabError,
  data: tabList,
  run: tabRun,
} = useRequest<ArticleListRes>(() => articleListApi({ articleTypeId: 1 }), {
  immediate: false,
  transform: res =>
    res[0].itemList.map((item) => {
      return { ...item, name: item.typeName }
    }),
})

onMounted(() => {
  tabRun().then(() => {
    getListData()
  })
})

function getListData() {
  list.value = tabList.value[tab.value].articleList
}

function handleSearch() {
  uni.navigateTo({
    url: '/pages/tutorials/searchPage',
  })
}
</script>

<template>
  <view class="bg-white px-4 py-2" @click="handleSearch">
    <view class="o-bg-no flex items-center gap-2 py-1 pl-4 pr-3" style="border-radius: 2rem">
      <up-input
        :maxlength="50"
        border="none"
        class="o-bg-transparent grow"
        clearable
        placeholder="搜索标题"
      />
      <up-icon name="search" size="20" />
    </view>
  </view>
  <up-sticky bg-color="#fff">
    <up-tabs
      v-model:current="tab"
      :line-width="50"
      :list="tabList"
      :line-color="Color.primary"
      @change="getListData"
    />
  </up-sticky>
  <view class="o-bg-no">
    <view class="px-3 pb-6 pt-3">
      <article-card v-for="subItem in list" :key="subItem.articleId" :data="subItem" />
    </view>
  </view>
  <up-back-top :scroll-top="scrollTop" />
</template>

<style lang="scss" scoped></style>
