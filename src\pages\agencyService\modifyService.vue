<route lang="json5">
{
  style: {
    navigationBarTitleText: '条码成员信息变更',
  },
}
</route>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import ModifyStep from '@/components/agencyService/modifyStep.vue'
import ModifyTip from '@/components/agencyService/modifyTip.vue'
import CsLongButton from '@/components/customer/csLongButton.vue'
import ServerRadioGroup from '@/components/serverRadioGroup.vue'
import ServerTitle from '@/components/serverTitle.vue'
import { ServerType } from '@/enums'
import { useCreateOrder } from '@/hooks/useCreateOrder'
import { useUserStore } from '@/store/user'
import { toAgencyPath } from '@/utils'

const userStore = useUserStore()
const { existUncompletedChange } = storeToRefs(userStore)

const { loading, isShowStep, serveData, selectServe, descriptionServiceType, handleSubmit }
  = useCreateOrder({
    orderConfirmUrl: '/pages/orderConfirm/default',
    paymentSuccessUrl: '/pages/paymentSuccess/agencyService',
  })
</script>

<template>
  <ServerTitle />
  <view class="mt-3 px-4">
    <view class="flex flex-col items-center justify-center rd-2 bg-white p-4">
      <view v-if="isShowStep" class="mb-6 pt-2">
        <ModifyTip />
        <view class="o-line mt-4" />
        <ModifyStep />
      </view>
      <view class="o-color-aid flex gap-3 text-xs" @click="isShowStep = !isShowStep">
        <up-icon name="question-circle" size="18" />
        <view>{{ isShowStep ? '隐藏' : '显示' }}办理说明</view>
        <up-icon v-if="isShowStep" name="arrow-up" size="16" />
        <up-icon v-else name="arrow-down" size="16" />
      </view>
    </view>
    <cs-long-button />
    <view class="mt-3 rd-2 bg-white p-4">
      <view class="mb-3 pt-4 font-bold">
        服务方案：
      </view>
      <view id="targetElement" class="mt-4">
        <server-radio-group v-model="selectServe" :items="serveData" />
      </view>
    </view>
    <view class="p-10" />
    <view class="fixed bottom-0 left-0 z-10 box-border w-full p-4">
      <view class="flex gap-1">
        <view
          class="flex flex-grow-1 items-center justify-center rd-2 py-3 font-bold"
          :class="
            existUncompletedChange ? 'bg-white o-color-primary border' : 'o-bg-primary o-shadow-blue color-white'
          "
          @click="handleSubmit"
        >
          {{ existUncompletedChange ? '继续下单' : '提交方案' }}
        </view>
        <view
          v-if="existUncompletedChange"
          class="o-bg-primary o-shadow-blue flex flex-grow-1 items-baseline justify-center rd-2 py-3 color-white"
          @click="toAgencyPath(ServerType.modifyService)"
        >
          <text class="text-sm">
            您有未完成的业务，
          </text>
          <text class="font-bold">
            前往办理
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
