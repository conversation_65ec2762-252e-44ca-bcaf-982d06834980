<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的业务',
  },
}
</route>

<script lang="ts" setup>
import type {
  OrderOtherInfoListRes,
  OrderOtherInfoListResData,
} from '@/service/agencyServiceApi'
import { storeToRefs } from 'pinia'
import ModifyStep from '@/components/agencyService/modifyStep.vue'
import ModifyTip from '@/components/agencyService/modifyTip.vue'
import RegisterStep from '@/components/agencyService/registerStep.vue'
import RenewalStep from '@/components/agencyService/renewalStep.vue'
import RenewalTip from '@/components/agencyService/renewalTip.vue'
import CsLongButton from '@/components/customer/csLongButton.vue'
import { PROJECT_3 } from '@/components/descriptionStr'
import { OrderStatus, ServerType } from '@/enums'
import { useToPath } from '@/hooks/useToPath'
import {
  orderOtherInfoListApi,
} from '@/service/agencyServiceApi'
import { submitServiceStore } from '@/store/submitServiceStore'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const { registerServiceAuth, renewalServiceAuth, modifyServiceAuth, userId }
  = storeToRefs(userStore)
const useSubmitServiceStore = submitServiceStore()
const { orderInfoData } = storeToRefs(useSubmitServiceStore)

const { toPath } = useToPath()

const title = ref('')
const serviceAuth = ref(false)
const skipAuth = ref(false)
const isUnfold = ref(false)
const loading = ref(false)
const isShowEmptyBtn = ref(false)
const dataList = ref<OrderOtherInfoListResData[]>([])
const serviceData = ref()
const whichService = ref<ServerType>()

function getDataList() {
  return new Promise<OrderOtherInfoListRes>((resolve, reject) => {
    dataList.value = []
    orderOtherInfoListApi({
      serverType: whichService.value,
      userId: userId.value,
    })
      .then((res) => {
        // 倒序
        dataList.value = [...res.data].reverse()
        resolve(res)
      })
      .catch((err) => {
        reject(err)
      })
      .finally(() => {
        loading.value = false
      })
  })
}

function getColor(status: OrderStatus) {
  switch (status) {
    case OrderStatus.failed:
      return 'color-red'
    case OrderStatus.waitingSubmit:
      return 'color-yellow'
    case OrderStatus.done:
      return 'color-gray'
    default:
      return 'text-primary'
  }
}

function toService(data: OrderOtherInfoListResData) {
  orderInfoData.value = data
  let url = ''
  if (whichService.value) {
    switch (whichService.value) {
      case ServerType.renewalService:
        url = '/pages/myAgency/submitRenewal'
        break
      case ServerType.registerService:
        url = '/pages/myAgency/submitRegister'
        break
      case ServerType.modifyService:
        url = '/pages/myAgency/submitModify'
        break
    }
    uni.navigateTo({
      url,
    })
  }
}

function handleRefresh() {
  loading.value = true
  getDataList()
}

onLoad((option: any) => {
  // 跳过授权，用于成交后马上跳转
  skipAuth.value = option.skipAuth === '1'
  whichService.value = Number(option?.serverType) as ServerType
  if (whichService.value) {
    switch (whichService.value) {
      case ServerType.renewalService:
        title.value = '续展'
        serviceAuth.value = renewalServiceAuth.value
        serviceData.value = PROJECT_3.filter(
          item => item.descriptionServiceType === 'renewalService',
        )[0]
        break
      case ServerType.registerService:
        title.value = '注册'
        serviceAuth.value = registerServiceAuth.value
        serviceData.value = PROJECT_3.filter(
          item => item.descriptionServiceType === 'registerService',
        )[0]
        break
      case ServerType.modifyService:
        title.value = '变更'
        serviceAuth.value = modifyServiceAuth.value
        serviceData.value = PROJECT_3.filter(
          item => item.descriptionServiceType === 'modifyService',
        )[0]
        break
    }
    uni.setNavigationBarTitle({
      title: `我的${title.value}`,
    })
  }
})

onShow(() => {
  if (skipAuth.value) {
    loading.value = true
    setTimeout(() => {
      getDataList().then((res) => {
        isShowEmptyBtn.value = res.data.length === 0
      })
    }, 3000)
  }
  else if (serviceAuth.value) {
    loading.value = true
    getDataList()
  }
})
</script>

<template>
  <view v-if="skipAuth || serviceAuth">
    <view class="bg-white p-6">
      <RegisterStep v-if="whichService === ServerType.registerService" />
      <view
        v-if="whichService === ServerType.renewalService"
        class="relative overflow-hidden"
        :style="isUnfold ? 'height: auto' : 'height: 80vw'"
      >
        <RenewalStep />
        <view
          v-if="!isUnfold"
          class="f-list-hidden absolute bottom-0 left-0 z-1 w-full"
          @click="isUnfold = true"
        >
          <view class="left absolute bottom-0 w-full text-center text-xs">
            展开
          </view>
        </view>
      </view>
      <ModifyStep v-if="whichService === ServerType.modifyService" />
    </view>
    <cs-long-button />
    <view class="px-4">
      <view class="mt-4 font-bold">
        {{ title }}服务情况：
      </view>
      <view v-for="item in dataList" :key="item.orderId" class="mt-2 rd-2 bg-white p-4">
        <view class="flex">
          <view class="grow-1">
            <view class="text-xs color-gray">
              支付时间：{{ item.payDate }}
            </view>
            <view class="mb-2 text-xs color-gray">
              订单编号：{{ item.orderCode }}
            </view>
            <view>{{ item.orderContent }}</view>
          </view>
          <view class="shrink-0" :class="getColor(item.otherStatus)">
            {{ item.otherStatusStr }}
          </view>
        </view>
        <view v-if="item.otherStatus === OrderStatus.failed" class="text-sm color-red">
          {{ item.reason }}
        </view>
        <view
          class="o-bg-primary o-shadow-blue mt-3 flex flex-grow-1 items-center justify-center rd-2 py-1 color-white font-bold"
          @click="toService(item)"
        >
          前往办理
        </view>
      </view>
      <up-loadmore v-if="loading" status="loading" />
      <view
        v-if="isShowEmptyBtn"
        class="o-border mx-auto mt-4 w-fit rd-2 bg-white px-8 py-2"
        @click="handleRefresh"
      >
        点击刷新
      </view>
      <view class="py-10" />
    </view>
  </view>
  <view v-else class="bg-white p-6">
    <view class="mb-3 text-base font-bold">
      您未申请{{ title }}服务
    </view>
    <RenewalTip v-if="whichService === ServerType.renewalService" class="text-sm" />
    <ModifyTip v-if="whichService === ServerType.modifyService" />
    <view
      class="o-bg-primary o-shadow-blue flex flex-grow-1 items-center justify-center rd-2 p-3 color-white font-bold"
      @click="toPath(serviceData, 'navigateTo')"
    >
      前往办理{{ title }}业务
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-list-hidden {
  height: 26vw;
  background: linear-gradient(rgba(255, 255, 255, 0) 0%, #fff 70%);
}
</style>
