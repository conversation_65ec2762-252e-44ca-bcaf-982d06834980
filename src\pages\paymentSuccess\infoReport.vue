<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '支付成功',
  },
}
</route>

<script lang="ts" setup>
import type { ServerType } from '@/enums'
import { storeToRefs } from 'pinia'
import ReportSuccessStep from '@/components/infoReport/reportSuccessStep.vue'
import NeedPhoneForm from '@/components/needPhoneForm.vue'
import CouponCard from '@/components/Price/CouponCard.vue'
import PriceBox from '@/components/Price/PriceBox.vue'
import { getCouponApi } from '@/service/orderApi'
import { orderStore } from '@/store/orderStore'

const useOrderStore = orderStore()
const { orderCode, tempName, actuallyPrice } = storeToRefs(useOrderStore)

const couponName = ref('')
const couponPrice = ref(0)
const couponType = ref(2)
const description = ref('')
const discount = ref(0)
const serverType = ref<ServerType>()
const expirySeconds = ref(0)
const vendorCode = ref(0)
const showCoupon = ref(false)

getCouponApi({ orderCode: orderCode.value }).then((res: any) => {
  console.log(res)
  const d = res.data
  if (d.couponId) {
    couponName.value = d.couponName
    couponPrice.value = d.couponPrice
    couponType.value = d.couponType
    description.value = d.description
    discount.value = d.discount
    serverType.value = d.serverType
    expirySeconds.value = d.expirySeconds
    vendorCode.value = d.vendorCode
    showCoupon.value = true
  }
})
</script>

<template>
  <view class="px-4 pb-10 pt-4">
    <view class="o-color-primary pl-3 text-xl font-bold">
      订单支付成功！
    </view>
    <view v-if="showCoupon" class="o-color-primary pl-3 text-sm">
      恭喜获得一张折扣券
    </view>
    <view class="mt-2 rd-2 bg-white p-4">
      <view class="o-color-aid text-xs">
        订单编号：{{ orderCode }}
      </view>
      <view class="o-color-aid mt-2 text-xs">
        产品编码信息通报
      </view>
      <view class="mb-2 flex items-center">
        {{ tempName }}
      </view>
      <view class="flex items-baseline justify-end">
        <view class="text-sm">
          实付：
        </view>
        <price-box :price="actuallyPrice" :size="48" />
      </view>
    </view>
    <NeedPhoneForm />
    <view class="mt-3 rd-2 bg-white">
      <view class="p-4">
        <ReportSuccessStep />
      </view>
      <template v-if="showCoupon">
        <view class="f-coupon-dot mb-4 w-full" />
        <view class="p-4">
          <coupon-card
            :data="{
              couponName,
              couponPrice,
              couponType,
              description,
              serverType,
              discount,
              expirySeconds,
              vendorCode,
            }"
          />
        </view>
      </template>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-coupon-dot {
  height: 50rpx;
  background-image: url('https://wx.gs1helper.com/images/p_coupon_o_o_o.png');
  background-repeat: no-repeat;
  background-position-y: 50%;
  background-size: contain;
}
</style>
