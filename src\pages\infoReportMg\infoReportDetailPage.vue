<route lang="json5">
{
  style: {
    navigationBarTitleText: '产品详情',
    enablePullDownRefresh: true,
  },
}
</route>

<script lang="ts" setup>
import type { ReportGoodsLoadV2Res } from '@/service/infoReport'
import { reportGoodsLoadV2Api } from '@/service/infoReport'

const RESOURCES_URL = import.meta.env.VITE_RESOURCES_URL

const data = ref<ReportGoodsLoadV2Res['data']>()
const goodsId = ref<number>()

function getDetail() {
  reportGoodsLoadV2Api({
    // goodsId: goodsId.value
    goodsId: 936,
  }).then((res) => {
    data.value = res.data
    uni.stopPullDownRefresh()
  })
}

onLoad((options) => {
  goodsId.value = Number(options.id)
  getDetail()
})

onPullDownRefresh(() => {
  getDetail()
})
</script>

<template>
  <view class="bg-white p-6">
    <scroll-view :scroll-x="true" :show-scrollbar="false" class="w-full" @touchmove.stop @scroll.stop>
      <view class="w-max flex space-x-2">
        <image
          v-for="i in 6" :key="i" :src="`${RESOURCES_URL}/p_index_Information_upload.png`" mode="aspectFit"
          class="f-img-item shrink-0 rounded"
        />
      </view>
    </scroll-view>
    <view class="mt-3 text-sm text-gray-500">
      6977327040672
    </view>
    <view class="mb-4 font-bold leading-4">
      食用木薯淀粉（吸嘴袋）食用木薯淀粉（吸嘴袋）食用木薯淀粉（吸嘴袋）食用木薯淀粉（吸嘴袋）食用木薯淀粉（吸嘴袋）
    </view>
    <view class="f-d-box">
      <view class="f-d-title">
        通用名：
      </view>
      <view class="f-d-content">
        食用木薯淀粉（吸嘴袋）食用木薯淀粉吸嘴袋食用木薯淀
      </view>
    </view>
    <view class="f-d-box">
      <view class="f-d-title">
        品牌：
      </view>
      <view class="f-d-content">
        鱼米之乡
      </view>
    </view>
    <view class="f-d-box">
      <view class="f-d-title">
        规格：
      </view>
      <view class="f-d-content">
        2.5kg×10/件
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-img-list {
  touch-action: pan-x;
  -webkit-overflow-scrolling: touch;
  /* 增加iOS滚动流畅度 */
  scrollbar-width: none;
  /* Firefox */
}

.f-img-list::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari */
}

.f-img-item {
  $w: 244rpx;
  width: $w;
  height: $w;
  border: 1px solid #ccc;
}

.f-d-box {
  @apply flex text-sm leading-4 mt-2;
}

.f-d-title {
  @apply text-gray-500 shrink-0;
  width: 160rpx;
}
</style>
