<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '服务页面',
  },
}
</route>

<script lang="ts" setup>
import CsLongButton from '@/components/customer/csLongButton.vue'
</script>

<template>
  <view class="f-bg h-100 center overflow-hidden p-4">
    <cs-long-button content="服务请咨询客服" />
  </view>
</template>

<style lang="scss" scoped></style>
