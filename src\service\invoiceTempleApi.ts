// 参数接口
import { http } from '@/utils/http'

export interface InvoiceTempPageParams {
  companyName?: string
  creditCode?: string
  groupBy?: string
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: OrderDirectionType
  pageIndex?: number
  pageSize?: number
  userId: number
}

// 响应接口
export interface InvoiceTempPageRes {
  data: {
    address: string
    bank: string
    bankCode: string
    companyName: string
    contact: string
    contactPhone: string
    creditCode: string
    email: string
    invoiceTempId: number
    phone: string
    userId: number
  }[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  pageIndex: number
  pageSize: number
  success: boolean
  totalCount: number
  totalPages: number
}
/**
 * 模版分页
 * @param {object} params qry
 * @param {string} params.companyName 企业名称,发票抬头,下单获取发票填写
 * @param {string} params.creditCode 企业统一信用代码
 * @param {string} params.groupBy
 * @param {boolean} params.needTotalCount
 * @param {string} params.orderBy
 * @param {string} params.orderDirection
 * @param {number} params.pageIndex
 * @param {number} params.pageSize
 * @param {number} params.userId 用户id
 * @returns
 */
export function invoiceTempleApi(params: InvoiceTempPageParams) {
  return http.post<InvoiceTempPageRes>('/api/invoiceTempPage', params)
}

// 参数接口
export interface InvoiceTempCreateParams {
  address?: string
  bank?: string
  bankCode?: string
  companyName: string
  contact: string
  contactPhone: string
  creditCode: string
  email: string
  phone?: string
  userId: number
}

// 响应接口
export interface InvoiceTempCreateRes {
  data: {
    invoiceTempId: number
  }
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 新增开票模版
 * @param {object} params cmd
 * @param {string} params.address 注册地址
 * @param {string} params.bank 开户银行
 * @param {string} params.bankCode 银行账号
 * @param {string} params.companyName 企业名称,发票抬头,下单获取发票填写
 * @param {string} params.contact 联系人
 * @param {string} params.contactPhone 联系电话
 * @param {string} params.creditCode 企业统一信用代码
 * @param {string} params.email 邮箱，下单获取发票填写
 * @param {string} params.phone 注册电话
 * @param {number} params.userId 用户id
 * @returns
 */
export function invoiceTempCreateApi(params: InvoiceTempCreateParams) {
  return http.post<InvoiceTempCreateRes>('/api/invoiceTempCreate', params)
}

// 参数接口
export interface InvoiceTempDelParams {
  invoiceTempId: number
}

// 响应接口
export interface InvoiceTempDelRes {
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 删除开票模版
 * @param {object} params cmd
 * @param {number} params.invoiceTempId 模版id
 * @returns
 */
export function invoiceTempDelApi(params: InvoiceTempDelParams) {
  return http.post<InvoiceTempDelRes>('/api/invoiceTempDel', params)
}

// 参数接口
export interface InvoiceTempLoadParams {
  invoiceTempId: number
}

// 响应接口
export interface InvoiceTempLoadRes {
  data: {
    address: string
    bank: string
    bankCode: string
    companyName: string
    contact: string
    contactPhone: string
    creditCode: string
    email: string
    invoiceTempId: number
    phone: string
    userId: number
  }
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 加载开票模版
 * @param {object} params qry
 * @param hideErrorToast
 * @param {number} params.invoiceTempId 模版id
 * @param {boolean} params.hideErrorToast 是否隐藏错误提示
 * @returns
 */
export function invoiceTempLoadApi(params: InvoiceTempLoadParams, hideErrorToast?: boolean) {
  return http.post<InvoiceTempLoadRes>('/api/invoiceTempLoad', params, {}, hideErrorToast)
}

// 参数接口
export interface InvoiceTempUpdateParams {
  address?: string
  bank?: string
  bankCode?: string
  companyName: string
  contact: string
  contactPhone: string
  creditCode: string
  email: string
  invoiceTempId: number
  phone?: string
  userId: number
}

// 响应接口
export interface InvoiceTempUpdateRes {
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 编辑开票模版
 * @param {object} params cmd
 * @param {string} params.address 注册地址
 * @param {string} params.bank 开户银行
 * @param {string} params.bankCode 银行账号
 * @param {string} params.companyName 企业名称,发票抬头,下单获取发票填写
 * @param {string} params.contact 联系人
 * @param {string} params.contactPhone 联系电话
 * @param {string} params.creditCode 企业统一信用代码
 * @param {string} params.email 邮箱，下单获取发票填写
 * @param {number} params.invoiceTempId 模版id
 * @param {string} params.phone 注册电话
 * @param {number} params.userId 用户id
 * @returns
 */
export function invoiceTempUpdateApi(params: InvoiceTempUpdateParams) {
  return http.post<InvoiceTempUpdateRes>('/api/invoiceTempUpdate', params)
}
